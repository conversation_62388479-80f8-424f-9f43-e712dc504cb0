import { create } from "zustand";
import type { AlertSend } from "../types";

// 定义选中行状态的接口
interface SelectedRowsState {
  // 选中的行数据
  selectedRows: AlertSend[];
  // 选中的行键
  selectedRowKeys: React.Key[];

  // 设置选中的行
  setSelectedRows: (rows: AlertSend[]) => void;
  // 设置选中的行键
  setSelectedRowKeys: (keys: React.Key[]) => void;
  // 添加选中行
  addSelectedRow: (row: AlertSend) => void;
  // 移除选中行
  removeSelectedRow: (rowId: number) => void;
  // 清空选中
  clearSelection: () => void;

  // 批量设置选中状态
  setSelection: (rows: AlertSend[], keys: React.Key[]) => void;
}

export const useSelectedRowsStore = create<SelectedRowsState>((set) => ({
  selectedRows: [],
  selectedRowKeys: [],

  // 设置选中的行数据
  setSelectedRows: (rows: AlertSend[]) =>
    set({
      selectedRows: rows,
      selectedRowKeys: rows.map((row) => row.id),
    }),

  // 设置选中的行键
  setSelectedRowKeys: (keys: React.Key[]) => set({ selectedRowKeys: keys }),

  // 添加选中行
  addSelectedRow: (row: AlertSend) =>
    set((state) => {
      const isAlreadySelected = state.selectedRows.some((r) => r.id === row.id);
      if (isAlreadySelected) return state;

      return {
        selectedRows: [...state.selectedRows, row],
        selectedRowKeys: [...state.selectedRowKeys, row.id],
      };
    }),

  // 移除选中行
  removeSelectedRow: (rowId: number) =>
    set((state) => ({
      selectedRows: state.selectedRows.filter((r) => r.id !== rowId),
      selectedRowKeys: state.selectedRowKeys.filter((key) => key !== rowId),
    })),

  clearSelection: () =>
    set({
      selectedRows: [],
      selectedRowKeys: [],
    }),

  // 切换行选中状态
  toggleRowSelection: (row: AlertSend) =>
    set((state) => {
      const isSelected = state.selectedRows.some((r) => r.id === row.id);

      if (isSelected) {
        return {
          selectedRows: state.selectedRows.filter((r) => r.id !== row.id),
          selectedRowKeys: state.selectedRowKeys.filter(
            (key) => key !== row.id
          ),
        };
      } else {
        return {
          selectedRows: [...state.selectedRows, row],
          selectedRowKeys: [...state.selectedRowKeys, row.id],
        };
      }
    }),

  // 批量设置选中状态
  setSelection: (rows: AlertSend[], keys: React.Key[]) =>
    set({
      selectedRows: rows,
      selectedRowKeys: keys,
    }),
}));
